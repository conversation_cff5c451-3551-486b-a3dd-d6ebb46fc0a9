<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [key\key.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image key\key.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060960: Last Updated: Mon Aug 04 19:26:13 2025
<BR><P>
<H3>Maximum Stack Usage =        736 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
USART1_IRQHandler &rArr; HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; rt_ringbuffer_put &rArr; __aeabi_assert &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[1c]">ADC1_2_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1c]">ADC1_2_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[1c]">ADC1_2_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[39]">ADC3_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[4]">BusFault_Handler</a> from stm32f1xx_it.o(i.BusFault_Handler) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[1f]">CAN1_RX1_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[20]">CAN1_SCE_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[15]">DMA1_Channel1_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[16]">DMA1_Channel2_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[17]">DMA1_Channel3_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[18]">DMA1_Channel4_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[19]">DMA1_Channel5_IRQHandler</a> from stm32f1xx_it.o(i.DMA1_Channel5_IRQHandler) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[1a]">DMA1_Channel6_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[1b]">DMA1_Channel7_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[42]">DMA2_Channel1_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[43]">DMA2_Channel2_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[44]">DMA2_Channel3_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[45]">DMA2_Channel4_5_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[7]">DebugMon_Handler</a> from stm32f1xx_it.o(i.DebugMon_Handler) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[10]">EXTI0_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[32]">EXTI15_10_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[11]">EXTI1_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[12]">EXTI2_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[13]">EXTI3_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[14]">EXTI4_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[21]">EXTI9_5_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[e]">FLASH_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[3a]">FSMC_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[2]">HardFault_Handler</a> from stm32f1xx_it.o(i.HardFault_Handler) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[2a]">I2C1_ER_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[29]">I2C1_EV_IRQHandler</a> from stm32f1xx_it.o(i.I2C1_EV_IRQHandler) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[2c]">I2C2_ER_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[2b]">I2C2_EV_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[4a]">I2C_DMAAbort</a> from stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) referenced from stm32f1xx_hal_i2c.o(i.I2C_ITError)
 <LI><a href="#[4a]">I2C_DMAAbort</a> from stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) referenced from stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF)
 <LI><a href="#[3]">MemManage_Handler</a> from stm32f1xx_it.o(i.MemManage_Handler) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[1]">NMI_Handler</a> from stm32f1xx_it.o(i.NMI_Handler) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[b]">PVD_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[8]">PendSV_Handler</a> from stm32f1xx_it.o(i.PendSV_Handler) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[f]">RCC_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[33]">RTC_Alarm_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[d]">RTC_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[0]">Reset_Handler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[3b]">SDIO_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[2d]">SPI1_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[2e]">SPI2_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[3d]">SPI3_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[6]">SVC_Handler</a> from stm32f1xx_it.o(i.SVC_Handler) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[9]">SysTick_Handler</a> from stm32f1xx_it.o(i.SysTick_Handler) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[47]">SystemInit</a> from system_stm32f1xx.o(i.SystemInit) referenced from startup_stm32f103xe.o(.text)
 <LI><a href="#[c]">TAMPER_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[22]">TIM1_BRK_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[25]">TIM1_CC_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[24]">TIM1_TRG_COM_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[23]">TIM1_UP_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[26]">TIM2_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[27]">TIM3_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[28]">TIM4_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[3c]">TIM5_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[40]">TIM6_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[41]">TIM7_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[35]">TIM8_BRK_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[38]">TIM8_CC_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[37]">TIM8_TRG_COM_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[36]">TIM8_UP_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[3e]">UART4_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[3f]">UART5_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[49]">UART_DMAAbortOnError</a> from stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError) referenced from stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler)
 <LI><a href="#[4d]">UART_DMAError</a> from stm32f1xx_hal_uart.o(i.UART_DMAError) referenced from stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA)
 <LI><a href="#[4b]">UART_DMAReceiveCplt</a> from stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) referenced from stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA)
 <LI><a href="#[4c]">UART_DMARxHalfCplt</a> from stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) referenced from stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA)
 <LI><a href="#[2f]">USART1_IRQHandler</a> from stm32f1xx_it.o(i.USART1_IRQHandler) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[30]">USART2_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[31]">USART3_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[34]">USBWakeUp_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[1d]">USB_HP_CAN1_TX_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[1e]">USB_LP_CAN1_RX0_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[5]">UsageFault_Handler</a> from stm32f1xx_it.o(i.UsageFault_Handler) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[a]">WWDG_IRQHandler</a> from startup_stm32f103xe.o(.text) referenced from startup_stm32f103xe.o(RESET)
 <LI><a href="#[48]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_stm32f103xe.o(.text)
 <LI><a href="#[4e]">_snputc</a> from printfa.o(i._snputc) referenced from printfa.o(i.__0vsnprintf)
 <LI><a href="#[68]">btn_task</a> from btn_app.o(i.btn_task) referenced from scheduler.o(.data)
 <LI><a href="#[67]">key_task</a> from key_app.o(i.key_task) referenced from scheduler.o(.data)
 <LI><a href="#[66]">led_task</a> from led_app.o(i.led_task) referenced from scheduler.o(.data)
 <LI><a href="#[46]">main</a> from main.o(i.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
 <LI><a href="#[6a]">oled_task</a> from oled_app.o(i.oled_task) referenced from scheduler.o(.data)
 <LI><a href="#[4f]">prv_btn_event</a> from btn_app.o(i.prv_btn_event) referenced from btn_app.o(i.app_ebtn_init)
 <LI><a href="#[50]">prv_btn_get_state</a> from btn_app.o(i.prv_btn_get_state) referenced from btn_app.o(i.app_ebtn_init)
 <LI><a href="#[64]">u8g2_draw_l90_mirrorr_r0</a> from u8g2_setup.o(i.u8g2_draw_l90_mirrorr_r0) referenced from u8g2_setup.o(.constdata)
 <LI><a href="#[5a]">u8g2_draw_l90_r0</a> from u8g2_setup.o(i.u8g2_draw_l90_r0) referenced from u8g2_setup.o(.constdata)
 <LI><a href="#[5d]">u8g2_draw_l90_r1</a> from u8g2_setup.o(i.u8g2_draw_l90_r1) referenced from u8g2_setup.o(.constdata)
 <LI><a href="#[60]">u8g2_draw_l90_r2</a> from u8g2_setup.o(i.u8g2_draw_l90_r2) referenced from u8g2_setup.o(.constdata)
 <LI><a href="#[63]">u8g2_draw_l90_r3</a> from u8g2_setup.o(i.u8g2_draw_l90_r3) referenced from u8g2_setup.o(.constdata)
 <LI><a href="#[65]">u8g2_draw_mirror_vertical_r0</a> from u8g2_setup.o(i.u8g2_draw_mirror_vertical_r0) referenced from u8g2_setup.o(.constdata)
 <LI><a href="#[53]">u8g2_font_calc_vref_font</a> from u8g2_font.o(i.u8g2_font_calc_vref_font) referenced from u8g2_font.o(i.u8g2_SetFontPosBaseline)
 <LI><a href="#[51]">u8g2_gpio_and_delay_stm32</a> from oled_app.o(i.u8g2_gpio_and_delay_stm32) referenced from main.o(i.main)
 <LI><a href="#[56]">u8g2_ll_hvline_vertical_top_lsb</a> from u8g2_ll_hvline.o(i.u8g2_ll_hvline_vertical_top_lsb) referenced from u8g2_d_setup.o(i.u8g2_Setup_ssd1306_i2c_128x64_noname_f)
 <LI><a href="#[58]">u8g2_update_dimension_r0</a> from u8g2_setup.o(i.u8g2_update_dimension_r0) referenced 3 times from u8g2_setup.o(.constdata)
 <LI><a href="#[5b]">u8g2_update_dimension_r1</a> from u8g2_setup.o(i.u8g2_update_dimension_r1) referenced from u8g2_setup.o(.constdata)
 <LI><a href="#[5e]">u8g2_update_dimension_r2</a> from u8g2_setup.o(i.u8g2_update_dimension_r2) referenced from u8g2_setup.o(.constdata)
 <LI><a href="#[61]">u8g2_update_dimension_r3</a> from u8g2_setup.o(i.u8g2_update_dimension_r3) referenced from u8g2_setup.o(.constdata)
 <LI><a href="#[59]">u8g2_update_page_win_r0</a> from u8g2_setup.o(i.u8g2_update_page_win_r0) referenced 3 times from u8g2_setup.o(.constdata)
 <LI><a href="#[5c]">u8g2_update_page_win_r1</a> from u8g2_setup.o(i.u8g2_update_page_win_r1) referenced from u8g2_setup.o(.constdata)
 <LI><a href="#[5f]">u8g2_update_page_win_r2</a> from u8g2_setup.o(i.u8g2_update_page_win_r2) referenced from u8g2_setup.o(.constdata)
 <LI><a href="#[62]">u8g2_update_page_win_r3</a> from u8g2_setup.o(i.u8g2_update_page_win_r3) referenced from u8g2_setup.o(.constdata)
 <LI><a href="#[52]">u8x8_byte_hw_i2c</a> from oled_app.o(i.u8x8_byte_hw_i2c) referenced from main.o(i.main)
 <LI><a href="#[54]">u8x8_cad_001</a> from u8x8_cad.o(i.u8x8_cad_001) referenced from u8g2_d_setup.o(i.u8g2_Setup_ssd1306_i2c_128x64_noname_f)
 <LI><a href="#[55]">u8x8_d_ssd1306_128x64_noname</a> from u8x8_d_ssd1306_128x64_noname.o(i.u8x8_d_ssd1306_128x64_noname) referenced from u8g2_d_setup.o(i.u8g2_Setup_ssd1306_i2c_128x64_noname_f)
 <LI><a href="#[57]">u8x8_dummy_cb</a> from u8x8_setup.o(i.u8x8_dummy_cb) referenced from u8x8_setup.o(i.u8x8_SetupDefaults)
 <LI><a href="#[69]">uart_task</a> from usart_app.o(i.uart_task) referenced from scheduler.o(.data)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[48]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(.text)
</UL>
<P><STRONG><a name="[132]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[6b]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[7c]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[133]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[134]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[135]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[136]"></a>__rt_lib_shutdown_fini</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry12b.o(.ARM.Collect$$$$0000000E))

<P><STRONG><a name="[137]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[138]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$00000011))

<P><STRONG><a name="[0]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>ADC1_2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_2_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_2_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>ADC3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>DMA1_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>DMA1_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>DMA1_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA1_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA1_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>DMA2_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>DMA2_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>DMA2_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>DMA2_Channel4_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>FSMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>RTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>TAMPER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>TIM1_BRK_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>TIM1_TRG_COM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>TIM1_UP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>TIM6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>TIM7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>TIM8_BRK_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>TIM8_TRG_COM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>TIM8_UP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>UART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>USART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>USBWakeUp_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>USB_HP_CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>USB_LP_CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[71]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[139]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[10f]"></a>__aeabi_memcpy</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_put
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_get
</UL>

<P><STRONG><a name="[fc]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_with_curr_state
</UL>

<P><STRONG><a name="[13a]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[6e]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[13b]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[13c]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[6d]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_ClearBuffer
</UL>

<P><STRONG><a name="[b1]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_with_curr_state
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_init
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_task
</UL>

<P><STRONG><a name="[13d]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[6f]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[f0]"></a>memcmp</STRONG> (Thumb, 26 bytes, Stack size 12 bytes, memcmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bit_array_cmp
</UL>

<P><STRONG><a name="[13e]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)

<P><STRONG><a name="[e9]"></a>__aeabi_uidivmod</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[70]"></a>__aeabi_uldivmod</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uldiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[13f]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 48 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[73]"></a>__aeabi_dadd</STRONG> (Thumb, 322 bytes, Stack size 48 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[77]"></a>__aeabi_dsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[78]"></a>__aeabi_drsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[79]"></a>__aeabi_dmul</STRONG> (Thumb, 228 bytes, Stack size 48 bytes, dmul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[7a]"></a>__aeabi_ddiv</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, ddiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[7b]"></a>__aeabi_d2ulz</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, dfixul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[e6]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdrcmple.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[6c]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[140]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[72]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[141]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[74]"></a>__aeabi_lasr</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[142]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[76]"></a>_double_round</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, depilogue.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[75]"></a>_double_epilogue</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, depilogue.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[4]"></a>BusFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.BusFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA1_Channel5_IRQHandler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, stm32f1xx_it.o(i.DMA1_Channel5_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = DMA1_Channel5_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[7]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[bf]"></a>Error_Handler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, main.o(i.Error_Handler))
<BR><BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[b2]"></a>HAL_DMA_Abort</STRONG> (Thumb, 86 bytes, Stack size 0 bytes, stm32f1xx_hal_dma.o(i.HAL_DMA_Abort))
<BR><BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
</UL>

<P><STRONG><a name="[b7]"></a>HAL_DMA_Abort_IT</STRONG> (Thumb, 414 bytes, Stack size 16 bytes, stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_DMA_Abort_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Slave_STOPF
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_ITError
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[d0]"></a>HAL_DMA_GetState</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f1xx_hal_dma.o(i.HAL_DMA_GetState))
<BR><BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Slave_STOPF
</UL>

<P><STRONG><a name="[7d]"></a>HAL_DMA_IRQHandler</STRONG> (Thumb, 910 bytes, Stack size 16 bytes, stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_DMA_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel5_IRQHandler
</UL>

<P><STRONG><a name="[be]"></a>HAL_DMA_Init</STRONG> (Thumb, 132 bytes, Stack size 0 bytes, stm32f1xx_hal_dma.o(i.HAL_DMA_Init))
<BR><BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
</UL>

<P><STRONG><a name="[7e]"></a>HAL_DMA_Start_IT</STRONG> (Thumb, 156 bytes, Stack size 24 bytes, stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Start_Receive_DMA
</UL>

<P><STRONG><a name="[80]"></a>HAL_Delay</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, stm32f1xx_hal.o(i.HAL_Delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_gpio_and_delay_stm32
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[9c]"></a>HAL_GPIO_Init</STRONG> (Thumb, 792 bytes, Stack size 36 bytes, stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
</UL>

<P><STRONG><a name="[fe]"></a>HAL_GPIO_ReadPin</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin))
<BR><BR>[Called By]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prv_btn_get_state
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_read
</UL>

<P><STRONG><a name="[d4]"></a>HAL_GPIO_WritePin</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin))
<BR><BR>[Called By]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_disp
</UL>

<P><STRONG><a name="[81]"></a>HAL_GetTick</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f1xx_hal.o(i.HAL_GetTick))
<BR><BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnMasterAddressFlagUntilTimeout
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnBTFFlagUntilTimeout
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_run
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;btn_task
</UL>

<P><STRONG><a name="[c2]"></a>HAL_I2C_AbortCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_ITError
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_DMAAbort
</UL>

<P><STRONG><a name="[cf]"></a>HAL_I2C_AddrCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_hal_i2c.o(i.HAL_I2C_AddrCallback))
<BR><BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Slave_ADDR
</UL>

<P><STRONG><a name="[82]"></a>HAL_I2C_EV_IRQHandler</STRONG> (Thumb, 474 bytes, Stack size 32 bytes, stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_I2C_EV_IRQHandler &rArr; I2C_Slave_STOPF &rArr; I2C_ITError &rArr; HAL_DMA_Abort_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Slave_STOPF
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Slave_ADDR
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_SlaveTransmit_TXE
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_SlaveTransmit_BTF
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_SlaveReceive_RXNE
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_SlaveReceive_BTF
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MemoryTransmit_TXE_BTF
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Master_SB
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Master_ADDR
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Master_ADD10
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterTransmit_TXE
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterTransmit_BTF
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterReceive_RXNE
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterReceive_BTF
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_ConvertOtherXferOptions
</UL>
<BR>[Called By]<UL><LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C1_EV_IRQHandler
</UL>

<P><STRONG><a name="[c3]"></a>HAL_I2C_ErrorCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback))
<BR><BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterReceive_RXNE
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_ITError
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_DMAAbort
</UL>

<P><STRONG><a name="[92]"></a>HAL_I2C_Init</STRONG> (Thumb, 446 bytes, Stack size 16 bytes, stm32f1xx_hal_i2c.o(i.HAL_I2C_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = HAL_I2C_Init &rArr; HAL_I2C_MspInit &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
</UL>

<P><STRONG><a name="[c5]"></a>HAL_I2C_ListenCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Slave_STOPF
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_ITError
</UL>

<P><STRONG><a name="[c7]"></a>HAL_I2C_MasterRxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterReceive_RXNE
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterReceive_BTF
</UL>

<P><STRONG><a name="[ca]"></a>HAL_I2C_MasterTxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterTransmit_TXE
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterTransmit_BTF
</UL>

<P><STRONG><a name="[95]"></a>HAL_I2C_Master_Transmit</STRONG> (Thumb, 360 bytes, Stack size 32 bytes, stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_I2C_Master_Transmit &rArr; I2C_MasterRequestWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnBTFFlagUntilTimeout
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterRequestWrite
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_hw_i2c
</UL>

<P><STRONG><a name="[c6]"></a>HAL_I2C_MemRxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterReceive_RXNE
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterReceive_BTF
</UL>

<P><STRONG><a name="[cb]"></a>HAL_I2C_MemTxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MemoryTransmit_TXE_BTF
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterTransmit_TXE
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterTransmit_BTF
</UL>

<P><STRONG><a name="[9a]"></a>HAL_I2C_Mem_Write</STRONG> (Thumb, 348 bytes, Stack size 48 bytes, stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnBTFFlagUntilTimeout
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>

<P><STRONG><a name="[93]"></a>HAL_I2C_MspInit</STRONG> (Thumb, 120 bytes, Stack size 32 bytes, i2c.o(i.HAL_I2C_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_I2C_MspInit &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
</UL>

<P><STRONG><a name="[cd]"></a>HAL_I2C_SlaveRxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Slave_STOPF
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_SlaveReceive_RXNE
</UL>

<P><STRONG><a name="[ce]"></a>HAL_I2C_SlaveTxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_SlaveTransmit_TXE
</UL>

<P><STRONG><a name="[dc]"></a>HAL_IncTick</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32f1xx_hal.o(i.HAL_IncTick))
<BR><BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[9f]"></a>HAL_Init</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, stm32f1xx_hal.o(i.HAL_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_Init &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriorityGrouping
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a1]"></a>HAL_InitTick</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, stm32f1xx_hal.o(i.HAL_InitTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = HAL_InitTick &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[a2]"></a>HAL_MspInit</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, stm32f1xx_hal_msp.o(i.HAL_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[9e]"></a>HAL_NVIC_EnableIRQ</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ))
<BR><BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
</UL>

<P><STRONG><a name="[9d]"></a>HAL_NVIC_SetPriority</STRONG> (Thumb, 124 bytes, Stack size 40 bytes, stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_GetPriorityGrouping
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[a0]"></a>HAL_NVIC_SetPriorityGrouping</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping))
<BR><BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[a6]"></a>HAL_RCC_ClockConfig</STRONG> (Thumb, 376 bytes, Stack size 16 bytes, stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_RCC_ClockConfig &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[a8]"></a>HAL_RCC_GetHCLKFreq</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq))
<BR><BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK2Freq
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
</UL>

<P><STRONG><a name="[94]"></a>HAL_RCC_GetPCLK1Freq</STRONG> (Thumb, 22 bytes, Stack size 4 bytes, stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_RCC_GetPCLK1Freq
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetHCLKFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[a9]"></a>HAL_RCC_GetPCLK2Freq</STRONG> (Thumb, 22 bytes, Stack size 4 bytes, stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_RCC_GetPCLK2Freq
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetHCLKFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[a7]"></a>HAL_RCC_GetSysClockFreq</STRONG> (Thumb, 92 bytes, Stack size 16 bytes, stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_RCC_GetSysClockFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
</UL>

<P><STRONG><a name="[aa]"></a>HAL_RCC_OscConfig</STRONG> (Thumb, 1114 bytes, Stack size 24 bytes, stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_RCC_OscConfig &rArr; RCC_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_Delay
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[a3]"></a>HAL_SYSTICK_Config</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_SYSTICK_Config &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[ac]"></a>HAL_UARTEx_ReceiveToIdle_DMA</STRONG> (Thumb, 112 bytes, Stack size 24 bytes, stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Start_Receive_DMA
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>

<P><STRONG><a name="[ae]"></a>HAL_UARTEx_RxEventCallback</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, usart_app.o(i.HAL_UARTEx_RxEventCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 664<LI>Call Chain = HAL_UARTEx_RxEventCallback &rArr; rt_ringbuffer_put &rArr; __aeabi_assert &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_put
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMARxHalfCplt
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAReceiveCplt
</UL>

<P><STRONG><a name="[af]"></a>HAL_UART_DMAStop</STRONG> (Thumb, 138 bytes, Stack size 16 bytes, stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_UART_DMAStop
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTxTransfer
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>

<P><STRONG><a name="[b8]"></a>HAL_UART_ErrorCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback))
<BR><BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAError
<LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAAbortOnError
</UL>

<P><STRONG><a name="[b5]"></a>HAL_UART_IRQHandler</STRONG> (Thumb, 736 bytes, Stack size 40 bytes, stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 728<LI>Call Chain = HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; rt_ringbuffer_put &rArr; __aeabi_assert &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Transmit_IT
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTransmit_IT
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort_IT
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[bb]"></a>HAL_UART_Init</STRONG> (Thumb, 118 bytes, Stack size 8 bytes, stm32f1xx_hal_uart.o(i.HAL_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
</UL>

<P><STRONG><a name="[bc]"></a>HAL_UART_MspInit</STRONG> (Thumb, 188 bytes, Stack size 32 bytes, usart.o(i.HAL_UART_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_UART_MspInit &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[de]"></a>HAL_UART_RxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_hal_uart.o(i.HAL_UART_RxCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAReceiveCplt
</UL>

<P><STRONG><a name="[df]"></a>HAL_UART_RxHalfCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMARxHalfCplt
</UL>

<P><STRONG><a name="[c0]"></a>HAL_UART_Transmit</STRONG> (Thumb, 190 bytes, Stack size 40 bytes, stm32f1xx_hal_uart.o(i.HAL_UART_Transmit))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>

<P><STRONG><a name="[e0]"></a>HAL_UART_TxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTransmit_IT
</UL>

<P><STRONG><a name="[2]"></a>HardFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.HardFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, stm32f1xx_it.o(i.I2C1_EV_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = I2C1_EV_IRQHandler &rArr; HAL_I2C_EV_IRQHandler &rArr; I2C_Slave_STOPF &rArr; I2C_ITError &rArr; HAL_DMA_Abort_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_EV_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[d2]"></a>MX_DMA_Init</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, dma.o(i.MX_DMA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = MX_DMA_Init &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[d3]"></a>MX_GPIO_Init</STRONG> (Thumb, 186 bytes, Stack size 24 bytes, gpio.o(i.MX_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = MX_GPIO_Init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[d5]"></a>MX_I2C1_Init</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, i2c.o(i.MX_I2C1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = MX_I2C1_Init &rArr; HAL_I2C_Init &rArr; HAL_I2C_MspInit &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[d6]"></a>MX_USART1_UART_Init</STRONG> (Thumb, 72 bytes, Stack size 8 bytes, usart.o(i.MX_USART1_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = MX_USART1_UART_Init &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[3]"></a>MemManage_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.MemManage_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[1]"></a>NMI_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.NMI_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[d7]"></a>OLED_Clear</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, oled.o(i.OLED_Clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = OLED_Clear &rArr; OLED_Write_data &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[da]"></a>OLED_Init</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, oled.o(i.OLED_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = OLED_Init &rArr; OLED_Set_Position &rArr; OLED_Write_cmd &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Set_Position
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[db]"></a>OLED_Set_Position</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, oled.o(i.OLED_Set_Position))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = OLED_Set_Position &rArr; OLED_Write_cmd &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[d8]"></a>OLED_Write_cmd</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, oled.o(i.OLED_Write_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = OLED_Write_cmd &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Set_Position
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
</UL>

<P><STRONG><a name="[d9]"></a>OLED_Write_data</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, oled.o(i.OLED_Write_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = OLED_Write_data &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
</UL>

<P><STRONG><a name="[8]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[6]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>SysTick_Handler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, stm32f1xx_it.o(i.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SysTick_Handler
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_IncTick
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[dd]"></a>SystemClock_Config</STRONG> (Thumb, 102 bytes, Stack size 64 bytes, main.o(i.SystemClock_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = SystemClock_Config &rArr; HAL_RCC_ClockConfig &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[47]"></a>SystemInit</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, system_stm32f1xx.o(i.SystemInit))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(.text)
</UL>
<P><STRONG><a name="[ad]"></a>UART_Start_Receive_DMA</STRONG> (Thumb, 186 bytes, Stack size 32 bytes, stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
</UL>

<P><STRONG><a name="[2f]"></a>USART1_IRQHandler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, stm32f1xx_it.o(i.USART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 736<LI>Call Chain = USART1_IRQHandler &rArr; HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; rt_ringbuffer_put &rArr; __aeabi_assert &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[5]"></a>UsageFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.UsageFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xe.o(RESET)
</UL>
<P><STRONG><a name="[e1]"></a>__0vsnprintf</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[143]"></a>__1vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)

<P><STRONG><a name="[144]"></a>__2vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)

<P><STRONG><a name="[145]"></a>__c89vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)

<P><STRONG><a name="[106]"></a>vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = vsnprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>

<P><STRONG><a name="[e3]"></a>__aeabi_assert</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, usart_app.o(i.__aeabi_assert))
<BR><BR>[Stack]<UL><LI>Max Depth = 624<LI>Call Chain = __aeabi_assert &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_init
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_put
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_get
</UL>

<P><STRONG><a name="[146]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[147]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[148]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[ea]"></a>app_ebtn_init</STRONG> (Thumb, 78 bytes, Stack size 24 bytes, btn_app.o(i.app_ebtn_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = app_ebtn_init &rArr; ebtn_init
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_set_config
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_init
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_get_btn_index_by_key_id
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_combo_btn_add_btn_by_idx
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[68]"></a>btn_task</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, btn_app.o(i.btn_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 148<LI>Call Chain = btn_task &rArr; ebtn_process &rArr; ebtn_process_with_curr_state &rArr; ebtn_process_btn_combo &rArr; bit_array_cmp &rArr; memcmp
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL>
<P><STRONG><a name="[ee]"></a>ebtn_combo_btn_add_btn_by_idx</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, ebtn.o(i.ebtn_combo_btn_add_btn_by_idx))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ebtn_combo_btn_add_btn_by_idx
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_ebtn_init
</UL>

<P><STRONG><a name="[ed]"></a>ebtn_get_btn_index_by_key_id</STRONG> (Thumb, 66 bytes, Stack size 12 bytes, ebtn.o(i.ebtn_get_btn_index_by_key_id))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = ebtn_get_btn_index_by_key_id
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_ebtn_init
</UL>

<P><STRONG><a name="[eb]"></a>ebtn_init</STRONG> (Thumb, 72 bytes, Stack size 32 bytes, ebtn.o(i.ebtn_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = ebtn_init
</UL>
<BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_ebtn_init
</UL>

<P><STRONG><a name="[f1]"></a>ebtn_process</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, ebtn.o(i.ebtn_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 140<LI>Call Chain = ebtn_process &rArr; ebtn_process_with_curr_state &rArr; ebtn_process_btn_combo &rArr; bit_array_cmp &rArr; memcmp
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_with_curr_state
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_get_current_state
</UL>
<BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;btn_task
</UL>

<P><STRONG><a name="[f4]"></a>ebtn_process_with_curr_state</STRONG> (Thumb, 444 bytes, Stack size 48 bytes, ebtn.o(i.ebtn_process_with_curr_state))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = ebtn_process_with_curr_state &rArr; ebtn_process_btn_combo &rArr; bit_array_cmp &rArr; memcmp
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_btn_combo
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_btn
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bit_array_or
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bit_array_get
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bit_array_cmp
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bit_array_and
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process
</UL>

<P><STRONG><a name="[ec]"></a>ebtn_set_config</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, ebtn.o(i.ebtn_set_config))
<BR><BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_ebtn_init
</UL>

<P><STRONG><a name="[fd]"></a>key_read</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, key_app.o(i.key_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = key_read
</UL>
<BR>[Calls]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_ReadPin
</UL>
<BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_task
</UL>

<P><STRONG><a name="[67]"></a>key_task</STRONG> (Thumb, 58 bytes, Stack size 8 bytes, key_app.o(i.key_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = key_task &rArr; key_read
</UL>
<BR>[Calls]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_read
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL>
<P><STRONG><a name="[ff]"></a>led_disp</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, led_app.o(i.led_disp))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = led_disp
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_task
</UL>

<P><STRONG><a name="[66]"></a>led_task</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, led_app.o(i.led_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = led_task &rArr; led_disp
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_disp
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL>
<P><STRONG><a name="[46]"></a>main</STRONG> (Thumb, 80 bytes, Stack size 0 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 640<LI>Call Chain = main &rArr; rt_ringbuffer_init &rArr; __aeabi_assert &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_SetPowerSave
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_InitDisplay
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_Setup_ssd1306_i2c_128x64_noname_f
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_run
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_init
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_init
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_ebtn_init
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[e4]"></a>my_printf</STRONG> (Thumb, 58 bytes, Stack size 544 bytes, usart_app.o(i.my_printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 616<LI>Call Chain = my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prv_btn_event
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_task
</UL>

<P><STRONG><a name="[6a]"></a>oled_task</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, oled_app.o(i.oled_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = oled_task &rArr; u8g2_DrawCircle &rArr; u8g2_draw_circle &rArr; u8g2_draw_circle_section &rArr; u8g2_DrawPixel &rArr; u8g2_DrawHVLine &rArr; u8g2_clip_intersection2
</UL>
<BR>[Calls]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SetDrawColor
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SendBuffer
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawCircle
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_ClearBuffer
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL>
<P><STRONG><a name="[4f]"></a>prv_btn_event</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, btn_app.o(i.prv_btn_event))
<BR><BR>[Stack]<UL><LI>Max Depth = 632<LI>Call Chain = prv_btn_event &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> btn_app.o(i.app_ebtn_init)
</UL>
<P><STRONG><a name="[50]"></a>prv_btn_get_state</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, btn_app.o(i.prv_btn_get_state))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = prv_btn_get_state
</UL>
<BR>[Calls]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_ReadPin
</UL>
<BR>[Address Reference Count : 1]<UL><LI> btn_app.o(i.app_ebtn_init)
</UL>
<P><STRONG><a name="[10c]"></a>rt_ringbuffer_data_len</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, ringbuffer.o(i.rt_ringbuffer_data_len))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rt_ringbuffer_data_len
</UL>
<BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_status
</UL>
<BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_put
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_get
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_task
</UL>

<P><STRONG><a name="[10e]"></a>rt_ringbuffer_get</STRONG> (Thumb, 180 bytes, Stack size 24 bytes, ringbuffer.o(i.rt_ringbuffer_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 648<LI>Call Chain = rt_ringbuffer_get &rArr; __aeabi_assert &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_data_len
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_task
</UL>

<P><STRONG><a name="[101]"></a>rt_ringbuffer_init</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, ringbuffer.o(i.rt_ringbuffer_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 640<LI>Call Chain = rt_ringbuffer_init &rArr; __aeabi_assert &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b0]"></a>rt_ringbuffer_put</STRONG> (Thumb, 182 bytes, Stack size 24 bytes, ringbuffer.o(i.rt_ringbuffer_put))
<BR><BR>[Stack]<UL><LI>Max Depth = 648<LI>Call Chain = rt_ringbuffer_put &rArr; __aeabi_assert &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_data_len
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>

<P><STRONG><a name="[100]"></a>scheduler_init</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, scheduler.o(i.scheduler_init))
<BR><BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[105]"></a>scheduler_run</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, scheduler.o(i.scheduler_run))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = scheduler_run
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[108]"></a>u8g2_ClearBuffer</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, u8g2_buffer.o(i.u8g2_ClearBuffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8g2_ClearBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_task
</UL>

<P><STRONG><a name="[109]"></a>u8g2_DrawCircle</STRONG> (Thumb, 70 bytes, Stack size 32 bytes, u8g2_circle.o(i.u8g2_DrawCircle))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = u8g2_DrawCircle &rArr; u8g2_draw_circle &rArr; u8g2_draw_circle_section &rArr; u8g2_DrawPixel &rArr; u8g2_DrawHVLine &rArr; u8g2_clip_intersection2
</UL>
<BR>[Calls]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_circle
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_IsIntersection
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_task
</UL>

<P><STRONG><a name="[112]"></a>u8g2_DrawHVLine</STRONG> (Thumb, 220 bytes, Stack size 40 bytes, u8g2_hvline.o(i.u8g2_DrawHVLine))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = u8g2_DrawHVLine &rArr; u8g2_clip_intersection2
</UL>
<BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_clip_intersection2
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawPixel
</UL>

<P><STRONG><a name="[114]"></a>u8g2_DrawPixel</STRONG> (Thumb, 68 bytes, Stack size 24 bytes, u8g2_hvline.o(i.u8g2_DrawPixel))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = u8g2_DrawPixel &rArr; u8g2_DrawHVLine &rArr; u8g2_clip_intersection2
</UL>
<BR>[Calls]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawHVLine
</UL>
<BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_circle_section
</UL>

<P><STRONG><a name="[110]"></a>u8g2_IsIntersection</STRONG> (Thumb, 58 bytes, Stack size 24 bytes, u8g2_intersection.o(i.u8g2_IsIntersection))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = u8g2_IsIntersection &rArr; u8g2_is_intersection_decision_tree
</UL>
<BR>[Calls]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_is_intersection_decision_tree
</UL>
<BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawCircle
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_apply_clip_window
</UL>

<P><STRONG><a name="[10a]"></a>u8g2_SendBuffer</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, u8g2_buffer.o(i.u8g2_SendBuffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = u8g2_SendBuffer &rArr; u8g2_send_buffer &rArr; u8g2_send_tile_row &rArr; u8x8_DrawTile
</UL>
<BR>[Calls]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_RefreshDisplay
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_send_buffer
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_task
</UL>

<P><STRONG><a name="[107]"></a>u8g2_SetDrawColor</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, u8g2_hvline.o(i.u8g2_SetDrawColor))
<BR><BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_task
</UL>

<P><STRONG><a name="[11a]"></a>u8g2_SetFontPosBaseline</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, u8g2_font.o(i.u8g2_SetFontPosBaseline))
<BR><BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SetupBuffer
</UL>

<P><STRONG><a name="[119]"></a>u8g2_SetMaxClipWindow</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, u8g2_setup.o(i.u8g2_SetMaxClipWindow))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = u8g2_SetMaxClipWindow
</UL>
<BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SetupBuffer
</UL>

<P><STRONG><a name="[118]"></a>u8g2_SetupBuffer</STRONG> (Thumb, 90 bytes, Stack size 24 bytes, u8g2_setup.o(i.u8g2_SetupBuffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = u8g2_SetupBuffer &rArr; u8g2_SetMaxClipWindow
</UL>
<BR>[Calls]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SetFontPosBaseline
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SetMaxClipWindow
</UL>
<BR>[Called By]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_Setup_ssd1306_i2c_128x64_noname_f
</UL>

<P><STRONG><a name="[102]"></a>u8g2_Setup_ssd1306_i2c_128x64_noname_f</STRONG> (Thumb, 54 bytes, Stack size 32 bytes, u8g2_d_setup.o(i.u8g2_Setup_ssd1306_i2c_128x64_noname_f))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = u8g2_Setup_ssd1306_i2c_128x64_noname_f &rArr; u8x8_Setup &rArr; u8x8_SetupMemory
</UL>
<BR>[Calls]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_Setup
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SetupBuffer
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_m_16_8_f
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[11f]"></a>u8g2_draw_hv_line_2dir</STRONG> (Thumb, 44 bytes, Stack size 32 bytes, u8g2_hvline.o(i.u8g2_draw_hv_line_2dir))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = u8g2_draw_hv_line_2dir
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_mirror_vertical_r0
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_l90_r3
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_l90_r2
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_l90_r1
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_l90_r0
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_l90_mirrorr_r0
</UL>

<P><STRONG><a name="[64]"></a>u8g2_draw_l90_mirrorr_r0</STRONG> (Thumb, 60 bytes, Stack size 32 bytes, u8g2_setup.o(i.u8g2_draw_l90_mirrorr_r0))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = u8g2_draw_l90_mirrorr_r0 &rArr; u8g2_draw_hv_line_2dir
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_hv_line_2dir
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_setup.o(.constdata)
</UL>
<P><STRONG><a name="[5a]"></a>u8g2_draw_l90_r0</STRONG> (Thumb, 36 bytes, Stack size 32 bytes, u8g2_setup.o(i.u8g2_draw_l90_r0))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = u8g2_draw_l90_r0 &rArr; u8g2_draw_hv_line_2dir
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_hv_line_2dir
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_setup.o(.constdata)
</UL>
<P><STRONG><a name="[5d]"></a>u8g2_draw_l90_r1</STRONG> (Thumb, 68 bytes, Stack size 40 bytes, u8g2_setup.o(i.u8g2_draw_l90_r1))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = u8g2_draw_l90_r1 &rArr; u8g2_draw_hv_line_2dir
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_hv_line_2dir
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_setup.o(.constdata)
</UL>
<P><STRONG><a name="[60]"></a>u8g2_draw_l90_r2</STRONG> (Thumb, 86 bytes, Stack size 40 bytes, u8g2_setup.o(i.u8g2_draw_l90_r2))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = u8g2_draw_l90_r2 &rArr; u8g2_draw_hv_line_2dir
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_hv_line_2dir
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_setup.o(.constdata)
</UL>
<P><STRONG><a name="[63]"></a>u8g2_draw_l90_r3</STRONG> (Thumb, 80 bytes, Stack size 40 bytes, u8g2_setup.o(i.u8g2_draw_l90_r3))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = u8g2_draw_l90_r3 &rArr; u8g2_draw_hv_line_2dir
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_hv_line_2dir
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_setup.o(.constdata)
</UL>
<P><STRONG><a name="[65]"></a>u8g2_draw_mirror_vertical_r0</STRONG> (Thumb, 60 bytes, Stack size 32 bytes, u8g2_setup.o(i.u8g2_draw_mirror_vertical_r0))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = u8g2_draw_mirror_vertical_r0 &rArr; u8g2_draw_hv_line_2dir
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_hv_line_2dir
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_setup.o(.constdata)
</UL>
<P><STRONG><a name="[53]"></a>u8g2_font_calc_vref_font</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, u8g2_font.o(i.u8g2_font_calc_vref_font))
<BR>[Address Reference Count : 1]<UL><LI> u8g2_font.o(i.u8g2_SetFontPosBaseline)
</UL>
<P><STRONG><a name="[51]"></a>u8g2_gpio_and_delay_stm32</STRONG> (Thumb, 160 bytes, Stack size 24 bytes, oled_app.o(i.u8g2_gpio_and_delay_stm32))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = u8g2_gpio_and_delay_stm32 &rArr; HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(i.main)
</UL>
<P><STRONG><a name="[115]"></a>u8g2_is_intersection_decision_tree</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, u8g2_intersection.o(i.u8g2_is_intersection_decision_tree))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = u8g2_is_intersection_decision_tree
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_IsIntersection
</UL>

<P><STRONG><a name="[56]"></a>u8g2_ll_hvline_vertical_top_lsb</STRONG> (Thumb, 238 bytes, Stack size 32 bytes, u8g2_ll_hvline.o(i.u8g2_ll_hvline_vertical_top_lsb))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = u8g2_ll_hvline_vertical_top_lsb
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_d_setup.o(i.u8g2_Setup_ssd1306_i2c_128x64_noname_f)
</UL>
<P><STRONG><a name="[11c]"></a>u8g2_m_16_8_f</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, u8g2_d_memory.o(i.u8g2_m_16_8_f))
<BR><BR>[Called By]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_Setup_ssd1306_i2c_128x64_noname_f
</UL>

<P><STRONG><a name="[58]"></a>u8g2_update_dimension_r0</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, u8g2_setup.o(i.u8g2_update_dimension_r0))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8g2_update_dimension_r0 &rArr; u8g2_update_dimension_common
</UL>
<BR>[Calls]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_update_dimension_common
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_setup.o(.constdata)
</UL>
<P><STRONG><a name="[5b]"></a>u8g2_update_dimension_r1</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, u8g2_setup.o(i.u8g2_update_dimension_r1))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8g2_update_dimension_r1 &rArr; u8g2_update_dimension_common
</UL>
<BR>[Calls]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_update_dimension_common
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_setup.o(.constdata)
</UL>
<P><STRONG><a name="[5e]"></a>u8g2_update_dimension_r2</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, u8g2_setup.o(i.u8g2_update_dimension_r2))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8g2_update_dimension_r2 &rArr; u8g2_update_dimension_common
</UL>
<BR>[Calls]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_update_dimension_common
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_setup.o(.constdata)
</UL>
<P><STRONG><a name="[61]"></a>u8g2_update_dimension_r3</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, u8g2_setup.o(i.u8g2_update_dimension_r3))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8g2_update_dimension_r3 &rArr; u8g2_update_dimension_common
</UL>
<BR>[Calls]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_update_dimension_common
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_setup.o(.constdata)
</UL>
<P><STRONG><a name="[59]"></a>u8g2_update_page_win_r0</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, u8g2_setup.o(i.u8g2_update_page_win_r0))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = u8g2_update_page_win_r0 &rArr; u8g2_apply_clip_window &rArr; u8g2_IsIntersection &rArr; u8g2_is_intersection_decision_tree
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_apply_clip_window
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_setup.o(.constdata)
</UL>
<P><STRONG><a name="[5c]"></a>u8g2_update_page_win_r1</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, u8g2_setup.o(i.u8g2_update_page_win_r1))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = u8g2_update_page_win_r1 &rArr; u8g2_apply_clip_window &rArr; u8g2_IsIntersection &rArr; u8g2_is_intersection_decision_tree
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_apply_clip_window
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_setup.o(.constdata)
</UL>
<P><STRONG><a name="[5f]"></a>u8g2_update_page_win_r2</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, u8g2_setup.o(i.u8g2_update_page_win_r2))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = u8g2_update_page_win_r2 &rArr; u8g2_apply_clip_window &rArr; u8g2_IsIntersection &rArr; u8g2_is_intersection_decision_tree
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_apply_clip_window
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_setup.o(.constdata)
</UL>
<P><STRONG><a name="[62]"></a>u8g2_update_page_win_r3</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, u8g2_setup.o(i.u8g2_update_page_win_r3))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = u8g2_update_page_win_r3 &rArr; u8g2_apply_clip_window &rArr; u8g2_IsIntersection &rArr; u8g2_is_intersection_decision_tree
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_apply_clip_window
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_setup.o(.constdata)
</UL>
<P><STRONG><a name="[121]"></a>u8x8_DrawTile</STRONG> (Thumb, 50 bytes, Stack size 32 bytes, u8x8_display.o(i.u8x8_DrawTile))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = u8x8_DrawTile
</UL>
<BR>[Called By]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_send_tile_row
</UL>

<P><STRONG><a name="[103]"></a>u8x8_InitDisplay</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, u8x8_display.o(i.u8x8_InitDisplay))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8x8_InitDisplay
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[117]"></a>u8x8_RefreshDisplay</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, u8x8_display.o(i.u8x8_RefreshDisplay))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8x8_RefreshDisplay
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SendBuffer
</UL>

<P><STRONG><a name="[104]"></a>u8x8_SetPowerSave</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, u8x8_display.o(i.u8x8_SetPowerSave))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8x8_SetPowerSave
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[11b]"></a>u8x8_Setup</STRONG> (Thumb, 42 bytes, Stack size 24 bytes, u8x8_setup.o(i.u8x8_Setup))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = u8x8_Setup &rArr; u8x8_SetupMemory
</UL>
<BR>[Calls]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_SetupMemory
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_SetupDefaults
</UL>
<BR>[Called By]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_Setup_ssd1306_i2c_128x64_noname_f
</UL>

<P><STRONG><a name="[123]"></a>u8x8_SetupDefaults</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, u8x8_setup.o(i.u8x8_SetupDefaults))
<BR><BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_Setup
</UL>

<P><STRONG><a name="[124]"></a>u8x8_SetupMemory</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, u8x8_display.o(i.u8x8_SetupMemory))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8x8_SetupMemory
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_Setup
</UL>

<P><STRONG><a name="[125]"></a>u8x8_byte_SendByte</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, u8x8_byte.o(i.u8x8_byte_SendByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = u8x8_byte_SendByte &rArr; u8x8_byte_SendBytes
</UL>
<BR>[Calls]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_SendBytes
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_001
</UL>

<P><STRONG><a name="[126]"></a>u8x8_byte_SendBytes</STRONG> (Thumb, 26 bytes, Stack size 24 bytes, u8x8_byte.o(i.u8x8_byte_SendBytes))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = u8x8_byte_SendBytes
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_SendByte
</UL>

<P><STRONG><a name="[127]"></a>u8x8_byte_SetDC</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, u8x8_byte.o(i.u8x8_byte_SetDC))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8x8_byte_SetDC
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_001
</UL>

<P><STRONG><a name="[52]"></a>u8x8_byte_hw_i2c</STRONG> (Thumb, 124 bytes, Stack size 32 bytes, oled_app.o(i.u8x8_byte_hw_i2c))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = u8x8_byte_hw_i2c &rArr; HAL_I2C_Master_Transmit &rArr; I2C_MasterRequestWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(i.main)
</UL>
<P><STRONG><a name="[54]"></a>u8x8_cad_001</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, u8x8_cad.o(i.u8x8_cad_001))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = u8x8_cad_001 &rArr; u8x8_byte_SendByte &rArr; u8x8_byte_SendBytes
</UL>
<BR>[Calls]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_SetDC
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_SendByte
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_d_setup.o(i.u8g2_Setup_ssd1306_i2c_128x64_noname_f)
</UL>
<P><STRONG><a name="[131]"></a>u8x8_cad_EndTransfer</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, u8x8_cad.o(i.u8x8_cad_EndTransfer))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8x8_cad_EndTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_sh1106_generic
</UL>

<P><STRONG><a name="[130]"></a>u8x8_cad_SendArg</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, u8x8_cad.o(i.u8x8_cad_SendArg))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8x8_cad_SendArg
</UL>
<BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_sh1106_generic
</UL>

<P><STRONG><a name="[12f]"></a>u8x8_cad_SendCmd</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, u8x8_cad.o(i.u8x8_cad_SendCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8x8_cad_SendCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_sh1106_generic
</UL>

<P><STRONG><a name="[129]"></a>u8x8_cad_SendData</STRONG> (Thumb, 26 bytes, Stack size 24 bytes, u8x8_cad.o(i.u8x8_cad_SendData))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = u8x8_cad_SendData
</UL>
<BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_sh1106_generic
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendSequence
</UL>

<P><STRONG><a name="[128]"></a>u8x8_cad_SendSequence</STRONG> (Thumb, 124 bytes, Stack size 24 bytes, u8x8_cad.o(i.u8x8_cad_SendSequence))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = u8x8_cad_SendSequence &rArr; u8x8_cad_SendData
</UL>
<BR>[Calls]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendData
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_gpio_call
</UL>
<BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_128x64_noname
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_sh1106_generic
</UL>

<P><STRONG><a name="[12e]"></a>u8x8_cad_StartTransfer</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, u8x8_cad.o(i.u8x8_cad_StartTransfer))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8x8_cad_StartTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_sh1106_generic
</UL>

<P><STRONG><a name="[12b]"></a>u8x8_d_helper_display_init</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, u8x8_display.o(i.u8x8_d_helper_display_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = u8x8_d_helper_display_init &rArr; u8x8_gpio_call
</UL>
<BR>[Calls]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_gpio_call
</UL>
<BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_128x64_noname
</UL>

<P><STRONG><a name="[12d]"></a>u8x8_d_helper_display_setup_memory</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, u8x8_display.o(i.u8x8_d_helper_display_setup_memory))
<BR><BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_128x64_noname
</UL>

<P><STRONG><a name="[55]"></a>u8x8_d_ssd1306_128x64_noname</STRONG> (Thumb, 76 bytes, Stack size 24 bytes, u8x8_d_ssd1306_128x64_noname.o(i.u8x8_d_ssd1306_128x64_noname))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = u8x8_d_ssd1306_128x64_noname &rArr; u8x8_d_ssd1306_sh1106_generic &rArr; u8x8_cad_SendSequence &rArr; u8x8_cad_SendData
</UL>
<BR>[Calls]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_helper_display_setup_memory
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_helper_display_init
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_sh1106_generic
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendSequence
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_d_setup.o(i.u8g2_Setup_ssd1306_i2c_128x64_noname_f)
</UL>
<P><STRONG><a name="[57]"></a>u8x8_dummy_cb</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, u8x8_setup.o(i.u8x8_dummy_cb))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = u8x8_dummy_cb
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8x8_setup.o(i.u8x8_SetupDefaults)
</UL>
<P><STRONG><a name="[12a]"></a>u8x8_gpio_call</STRONG> (Thumb, 26 bytes, Stack size 24 bytes, u8x8_gpio.o(i.u8x8_gpio_call))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = u8x8_gpio_call
</UL>
<BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_helper_display_init
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendSequence
</UL>

<P><STRONG><a name="[69]"></a>uart_task</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, usart_app.o(i.uart_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 656<LI>Call Chain = uart_task &rArr; rt_ringbuffer_get &rArr; __aeabi_assert &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_get
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_data_len
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL><P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[83]"></a>I2C_ConvertOtherXferOptions</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, stm32f1xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions))
<BR><BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_EV_IRQHandler
</UL>

<P><STRONG><a name="[4a]"></a>I2C_DMAAbort</STRONG> (Thumb, 242 bytes, Stack size 24 bytes, stm32f1xx_hal_i2c.o(i.I2C_DMAAbort))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = I2C_DMAAbort
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_ErrorCallback
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_AbortCpltCallback
</UL>
<BR>[Address Reference Count : 2]<UL><LI> stm32f1xx_hal_i2c.o(i.I2C_ITError)
<LI> stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF)
</UL>
<P><STRONG><a name="[cc]"></a>I2C_Flush_DR</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f1xx_hal_i2c.o(i.I2C_Flush_DR))
<BR><BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MemoryTransmit_TXE_BTF
</UL>

<P><STRONG><a name="[c4]"></a>I2C_ITError</STRONG> (Thumb, 420 bytes, Stack size 24 bytes, stm32f1xx_hal_i2c.o(i.I2C_ITError))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = I2C_ITError &rArr; HAL_DMA_Abort_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_ListenCpltCallback
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_ErrorCallback
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_AbortCpltCallback
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Slave_STOPF
</UL>

<P><STRONG><a name="[d1]"></a>I2C_IsAcknowledgeFailed</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed))
<BR><BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnBTFFlagUntilTimeout
</UL>

<P><STRONG><a name="[8b]"></a>I2C_MasterReceive_BTF</STRONG> (Thumb, 304 bytes, Stack size 16 bytes, stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = I2C_MasterReceive_BTF
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MemRxCpltCallback
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MasterRxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_EV_IRQHandler
</UL>

<P><STRONG><a name="[8a]"></a>I2C_MasterReceive_RXNE</STRONG> (Thumb, 260 bytes, Stack size 16 bytes, stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = I2C_MasterReceive_RXNE &rArr; I2C_WaitOnSTOPRequestThroughIT
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnSTOPRequestThroughIT
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MemRxCpltCallback
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MasterRxCpltCallback
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_ErrorCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_EV_IRQHandler
</UL>

<P><STRONG><a name="[97]"></a>I2C_MasterRequestWrite</STRONG> (Thumb, 186 bytes, Stack size 32 bytes, stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = I2C_MasterRequestWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnMasterAddressFlagUntilTimeout
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit
</UL>

<P><STRONG><a name="[88]"></a>I2C_MasterTransmit_BTF</STRONG> (Thumb, 160 bytes, Stack size 16 bytes, stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = I2C_MasterTransmit_BTF
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MemTxCpltCallback
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MasterTxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_EV_IRQHandler
</UL>

<P><STRONG><a name="[87]"></a>I2C_MasterTransmit_TXE</STRONG> (Thumb, 216 bytes, Stack size 24 bytes, stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = I2C_MasterTransmit_TXE &rArr; I2C_MemoryTransmit_TXE_BTF
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MemoryTransmit_TXE_BTF
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MemTxCpltCallback
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MasterTxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_EV_IRQHandler
</UL>

<P><STRONG><a name="[85]"></a>I2C_Master_ADD10</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, stm32f1xx_hal_i2c.o(i.I2C_Master_ADD10))
<BR><BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_EV_IRQHandler
</UL>

<P><STRONG><a name="[86]"></a>I2C_Master_ADDR</STRONG> (Thumb, 640 bytes, Stack size 16 bytes, stm32f1xx_hal_i2c.o(i.I2C_Master_ADDR))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = I2C_Master_ADDR
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_EV_IRQHandler
</UL>

<P><STRONG><a name="[84]"></a>I2C_Master_SB</STRONG> (Thumb, 158 bytes, Stack size 0 bytes, stm32f1xx_hal_i2c.o(i.I2C_Master_SB))
<BR><BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_EV_IRQHandler
</UL>

<P><STRONG><a name="[89]"></a>I2C_MemoryTransmit_TXE_BTF</STRONG> (Thumb, 198 bytes, Stack size 16 bytes, stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = I2C_MemoryTransmit_TXE_BTF
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Flush_DR
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MemTxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterTransmit_TXE
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_EV_IRQHandler
</UL>

<P><STRONG><a name="[9b]"></a>I2C_RequestMemoryWrite</STRONG> (Thumb, 220 bytes, Stack size 32 bytes, stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnMasterAddressFlagUntilTimeout
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>

<P><STRONG><a name="[91]"></a>I2C_SlaveReceive_BTF</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f1xx_hal_i2c.o(i.I2C_SlaveReceive_BTF))
<BR><BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_EV_IRQHandler
</UL>

<P><STRONG><a name="[90]"></a>I2C_SlaveReceive_RXNE</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, stm32f1xx_hal_i2c.o(i.I2C_SlaveReceive_RXNE))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = I2C_SlaveReceive_RXNE
</UL>
<BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_SlaveRxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_EV_IRQHandler
</UL>

<P><STRONG><a name="[8f]"></a>I2C_SlaveTransmit_BTF</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f1xx_hal_i2c.o(i.I2C_SlaveTransmit_BTF))
<BR><BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_EV_IRQHandler
</UL>

<P><STRONG><a name="[8e]"></a>I2C_SlaveTransmit_TXE</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, stm32f1xx_hal_i2c.o(i.I2C_SlaveTransmit_TXE))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = I2C_SlaveTransmit_TXE
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_SlaveTxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_EV_IRQHandler
</UL>

<P><STRONG><a name="[8c]"></a>I2C_Slave_ADDR</STRONG> (Thumb, 112 bytes, Stack size 24 bytes, stm32f1xx_hal_i2c.o(i.I2C_Slave_ADDR))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = I2C_Slave_ADDR
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_AddrCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_EV_IRQHandler
</UL>

<P><STRONG><a name="[8d]"></a>I2C_Slave_STOPF</STRONG> (Thumb, 386 bytes, Stack size 16 bytes, stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = I2C_Slave_STOPF &rArr; I2C_ITError &rArr; HAL_DMA_Abort_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_ITError
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_SlaveRxCpltCallback
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_ListenCpltCallback
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_GetState
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_EV_IRQHandler
</UL>

<P><STRONG><a name="[99]"></a>I2C_WaitOnBTFFlagUntilTimeout</STRONG> (Thumb, 102 bytes, Stack size 16 bytes, stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = I2C_WaitOnBTFFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_IsAcknowledgeFailed
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit
</UL>

<P><STRONG><a name="[96]"></a>I2C_WaitOnFlagUntilTimeout</STRONG> (Thumb, 190 bytes, Stack size 24 bytes, stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = I2C_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterRequestWrite
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit
</UL>

<P><STRONG><a name="[c9]"></a>I2C_WaitOnMasterAddressFlagUntilTimeout</STRONG> (Thumb, 250 bytes, Stack size 24 bytes, stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterRequestWrite
</UL>

<P><STRONG><a name="[c8]"></a>I2C_WaitOnSTOPRequestThroughIT</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, stm32f1xx_hal_i2c.o(i.I2C_WaitOnSTOPRequestThroughIT))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = I2C_WaitOnSTOPRequestThroughIT
</UL>
<BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterReceive_RXNE
</UL>

<P><STRONG><a name="[98]"></a>I2C_WaitOnTXEFlagUntilTimeout</STRONG> (Thumb, 102 bytes, Stack size 16 bytes, stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = I2C_WaitOnTXEFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_IsAcknowledgeFailed
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit
</UL>

<P><STRONG><a name="[ab]"></a>RCC_Delay</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, stm32f1xx_hal_rcc.o(i.RCC_Delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = RCC_Delay
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
</UL>

<P><STRONG><a name="[7f]"></a>DMA_SetConfig</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, stm32f1xx_hal_dma.o(i.DMA_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = DMA_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>

<P><STRONG><a name="[a4]"></a>__NVIC_GetPriorityGrouping</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f1xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping))
<BR><BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>

<P><STRONG><a name="[a5]"></a>__NVIC_SetPriority</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
</UL>

<P><STRONG><a name="[49]"></a>UART_DMAAbortOnError</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = UART_DMAAbortOnError
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler)
</UL>
<P><STRONG><a name="[4d]"></a>UART_DMAError</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, stm32f1xx_hal_uart.o(i.UART_DMAError))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = UART_DMAError
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTxTransfer
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA)
</UL>
<P><STRONG><a name="[4b]"></a>UART_DMAReceiveCplt</STRONG> (Thumb, 180 bytes, Stack size 16 bytes, stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 680<LI>Call Chain = UART_DMAReceiveCplt &rArr; HAL_UARTEx_RxEventCallback &rArr; rt_ringbuffer_put &rArr; __aeabi_assert &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA)
</UL>
<P><STRONG><a name="[4c]"></a>UART_DMARxHalfCplt</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 680<LI>Call Chain = UART_DMARxHalfCplt &rArr; HAL_UARTEx_RxEventCallback &rArr; rt_ringbuffer_put &rArr; __aeabi_assert &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxHalfCpltCallback
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA)
</UL>
<P><STRONG><a name="[b4]"></a>UART_EndRxTransfer</STRONG> (Thumb, 108 bytes, Stack size 0 bytes, stm32f1xx_hal_uart.o(i.UART_EndRxTransfer))
<BR><BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAError
</UL>

<P><STRONG><a name="[ba]"></a>UART_EndTransmit_IT</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, stm32f1xx_hal_uart.o(i.UART_EndTransmit_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UART_EndTransmit_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_TxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[b3]"></a>UART_EndTxTransfer</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, stm32f1xx_hal_uart.o(i.UART_EndTxTransfer))
<BR><BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAError
</UL>

<P><STRONG><a name="[b6]"></a>UART_Receive_IT</STRONG> (Thumb, 252 bytes, Stack size 24 bytes, stm32f1xx_hal_uart.o(i.UART_Receive_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 688<LI>Call Chain = UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; rt_ringbuffer_put &rArr; __aeabi_assert &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[bd]"></a>UART_SetConfig</STRONG> (Thumb, 248 bytes, Stack size 16 bytes, stm32f1xx_hal_uart.o(i.UART_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = UART_SetConfig &rArr; HAL_RCC_GetPCLK2Freq
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK2Freq
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[b9]"></a>UART_Transmit_IT</STRONG> (Thumb, 96 bytes, Stack size 0 bytes, stm32f1xx_hal_uart.o(i.UART_Transmit_IT))
<BR><BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[c1]"></a>UART_WaitOnFlagUntilTimeout</STRONG> (Thumb, 140 bytes, Stack size 32 bytes, stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
</UL>

<P><STRONG><a name="[fa]"></a>bit_array_and</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, ebtn.o(i.bit_array_and))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = bit_array_and
</UL>
<BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_with_curr_state
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_btn_combo
</UL>

<P><STRONG><a name="[f3]"></a>bit_array_assign</STRONG> (Thumb, 46 bytes, Stack size 12 bytes, ebtn.o(i.bit_array_assign))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = bit_array_assign
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_get_current_state
</UL>

<P><STRONG><a name="[ef]"></a>bit_array_cmp</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, ebtn.o(i.bit_array_cmp))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = bit_array_cmp &rArr; memcmp
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_with_curr_state
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_btn_combo
</UL>

<P><STRONG><a name="[f6]"></a>bit_array_get</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, ebtn.o(i.bit_array_get))
<BR><BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_with_curr_state
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_btn
</UL>

<P><STRONG><a name="[f9]"></a>bit_array_num_bits_set</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, ebtn.o(i.bit_array_num_bits_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = bit_array_num_bits_set
</UL>
<BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_btn_combo
</UL>

<P><STRONG><a name="[fb]"></a>bit_array_or</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, ebtn.o(i.bit_array_or))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = bit_array_or
</UL>
<BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_with_curr_state
</UL>

<P><STRONG><a name="[f2]"></a>ebtn_get_current_state</STRONG> (Thumb, 82 bytes, Stack size 24 bytes, ebtn.o(i.ebtn_get_current_state))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = ebtn_get_current_state &rArr; bit_array_assign
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bit_array_assign
</UL>
<BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process
</UL>

<P><STRONG><a name="[f5]"></a>ebtn_process_btn</STRONG> (Thumb, 56 bytes, Stack size 32 bytes, ebtn.o(i.ebtn_process_btn))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = ebtn_process_btn &rArr; prv_process_btn
</UL>
<BR>[Calls]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prv_process_btn
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bit_array_get
</UL>
<BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_with_curr_state
</UL>

<P><STRONG><a name="[f8]"></a>ebtn_process_btn_combo</STRONG> (Thumb, 116 bytes, Stack size 40 bytes, ebtn.o(i.ebtn_process_btn_combo))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = ebtn_process_btn_combo &rArr; bit_array_cmp &rArr; memcmp
</UL>
<BR>[Calls]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prv_process_btn
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bit_array_num_bits_set
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bit_array_cmp
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bit_array_and
</UL>
<BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_with_curr_state
</UL>

<P><STRONG><a name="[10b]"></a>ebtn_timer_sub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, ebtn.o(i.ebtn_timer_sub))
<BR><BR>[Called By]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prv_process_btn
</UL>

<P><STRONG><a name="[f7]"></a>prv_process_btn</STRONG> (Thumb, 482 bytes, Stack size 24 bytes, ebtn.o(i.prv_process_btn))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = prv_process_btn
</UL>
<BR>[Calls]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_timer_sub
</UL>
<BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_btn_combo
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_btn
</UL>

<P><STRONG><a name="[10d]"></a>rt_ringbuffer_status</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, ringbuffer.o(i.rt_ringbuffer_status))
<BR><BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_data_len
</UL>

<P><STRONG><a name="[116]"></a>u8g2_send_buffer</STRONG> (Thumb, 54 bytes, Stack size 24 bytes, u8g2_buffer.o(i.u8g2_send_buffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = u8g2_send_buffer &rArr; u8g2_send_tile_row &rArr; u8x8_DrawTile
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_send_tile_row
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SendBuffer
</UL>

<P><STRONG><a name="[120]"></a>u8g2_send_tile_row</STRONG> (Thumb, 54 bytes, Stack size 32 bytes, u8g2_buffer.o(i.u8g2_send_tile_row))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = u8g2_send_tile_row &rArr; u8x8_DrawTile
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_DrawTile
</UL>
<BR>[Called By]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_send_buffer
</UL>

<P><STRONG><a name="[111]"></a>u8g2_draw_circle</STRONG> (Thumb, 128 bytes, Stack size 64 bytes, u8g2_circle.o(i.u8g2_draw_circle))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = u8g2_draw_circle &rArr; u8g2_draw_circle_section &rArr; u8g2_DrawPixel &rArr; u8g2_DrawHVLine &rArr; u8g2_clip_intersection2
</UL>
<BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_circle_section
</UL>
<BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawCircle
</UL>

<P><STRONG><a name="[11e]"></a>u8g2_draw_circle_section</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, u8g2_circle.o(i.u8g2_draw_circle_section))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = u8g2_draw_circle_section &rArr; u8g2_DrawPixel &rArr; u8g2_DrawHVLine &rArr; u8g2_clip_intersection2
</UL>
<BR>[Calls]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawPixel
</UL>
<BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_circle
</UL>

<P><STRONG><a name="[113]"></a>u8g2_clip_intersection2</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, u8g2_hvline.o(i.u8g2_clip_intersection2))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8g2_clip_intersection2
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawHVLine
</UL>

<P><STRONG><a name="[11d]"></a>u8g2_apply_clip_window</STRONG> (Thumb, 122 bytes, Stack size 16 bytes, u8g2_setup.o(i.u8g2_apply_clip_window))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = u8g2_apply_clip_window &rArr; u8g2_IsIntersection &rArr; u8g2_is_intersection_decision_tree
</UL>
<BR>[Calls]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_IsIntersection
</UL>
<BR>[Called By]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_update_page_win_r3
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_update_page_win_r2
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_update_page_win_r1
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_update_page_win_r0
</UL>

<P><STRONG><a name="[122]"></a>u8g2_update_dimension_common</STRONG> (Thumb, 96 bytes, Stack size 8 bytes, u8g2_setup.o(i.u8g2_update_dimension_common))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = u8g2_update_dimension_common
</UL>
<BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_update_dimension_r3
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_update_dimension_r2
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_update_dimension_r1
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_update_dimension_r0
</UL>

<P><STRONG><a name="[12c]"></a>u8x8_d_ssd1306_sh1106_generic</STRONG> (Thumb, 242 bytes, Stack size 32 bytes, u8x8_d_ssd1306_128x64_noname.o(i.u8x8_d_ssd1306_sh1106_generic))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = u8x8_d_ssd1306_sh1106_generic &rArr; u8x8_cad_SendSequence &rArr; u8x8_cad_SendData
</UL>
<BR>[Calls]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_StartTransfer
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendSequence
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendData
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendCmd
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendArg
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_EndTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_128x64_noname
</UL>

<P><STRONG><a name="[e5]"></a>_fp_digits</STRONG> (Thumb, 366 bytes, Stack size 64 bytes, printfa.o(i._fp_digits), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[e2]"></a>_printf_core</STRONG> (Thumb, 1704 bytes, Stack size 136 bytes, printfa.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0vsnprintf
</UL>

<P><STRONG><a name="[e8]"></a>_printf_post_padding</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, printfa.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[e7]"></a>_printf_pre_padding</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, printfa.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[4e]"></a>_snputc</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, printfa.o(i._snputc))
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0vsnprintf)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
